import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_constants.dart';
import '../../data/models/category.dart';
import '../widgets/category_search_bar.dart';
import '../widgets/popular_category_card.dart';
import '../widgets/expandable_category_card.dart';
import '../widgets/section_header.dart';

class CategoryScreen extends StatefulWidget {
  const CategoryScreen({super.key});

  @override
  State<CategoryScreen> createState() => _CategoryScreenState();
}

class _CategoryScreenState extends State<CategoryScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<Category> _filteredCategories = [];
  List<Category> _allCategories = [];
  final int _cartItemCount = 3;

  @override
  void initState() {
    super.initState();
    _allCategories = CategoryData.getDetailedCategories();
    _filteredCategories = _allCategories;
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredCategories = _allCategories;
      } else {
        _filteredCategories = _allCategories.where((category) {
          final nameMatch = category.name.toLowerCase().contains(query);
          final descriptionMatch =
              category.description?.toLowerCase().contains(query) ?? false;
          final subcategoryMatch =
              category.subcategories?.any(
                (subcategory) => subcategory.toLowerCase().contains(query),
              ) ??
              false;
          return nameMatch || descriptionMatch || subcategoryMatch;
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFAFAFA),
      body: SafeArea(
        child: Column(
          children: [
            _buildAppBar(),
            Expanded(
              child: SingleChildScrollView(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        const Color(0xFFFAFAFA),
                        AppColors.primary.withValues(alpha: 0.02),
                        AppColors.secondary.withValues(alpha: 0.01),
                        Colors.white,
                      ],
                      stops: const [0.0, 0.3, 0.7, 1.0],
                    ),
                  ),
                  child: Column(
                    children: [
                      const SizedBox(height: AppConstants.paddingMedium),
                      _buildSearchSection(),
                      const SizedBox(height: AppConstants.paddingLarge),
                      _buildPopularCategoriesSection(),
                      const SizedBox(height: AppConstants.paddingLarge),
                      _buildAllCategoriesSection(),
                      const SizedBox(
                        height: 100,
                      ), // Bottom padding for navigation
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight.withValues(alpha: 0.08),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Logo
          Text(
            'logo',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontFamily: 'Pacifico',
              color: AppColors.primary,
              fontWeight: FontWeight.normal,
            ),
          ),
          const Spacer(),
          // Action Icons
          _buildActionIcon(Icons.wb_sunny_outlined),
          const SizedBox(width: AppConstants.paddingSmall),
          _buildActionIcon(Icons.search),
          const SizedBox(width: AppConstants.paddingSmall),
          _buildActionIcon(Icons.favorite_border),
          const SizedBox(width: AppConstants.paddingSmall),
          _buildCartIcon(),
        ],
      ),
    );
  }

  Widget _buildActionIcon(IconData icon) {
    return Container(
      width: 32,
      height: 32,
      decoration: const BoxDecoration(shape: BoxShape.circle),
      child: Icon(icon, size: 20, color: AppColors.textPrimary),
    );
  }

  Widget _buildCartIcon() {
    return Stack(
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: const BoxDecoration(shape: BoxShape.circle),
          child: Icon(
            Icons.shopping_cart_outlined,
            size: 20,
            color: AppColors.textPrimary,
          ),
        ),
        if (_cartItemCount > 0)
          Positioned(
            right: 0,
            top: 0,
            child: Container(
              width: 18,
              height: 18,
              decoration: const BoxDecoration(
                color: AppColors.primary,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  _cartItemCount.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildSearchSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
      ),
      child: CategorySearchBar(
        controller: _searchController,
        hintText: 'Search categories...',
        onChanged: (value) => _onSearchChanged(),
        onClear: () => _onSearchChanged(),
      ),
    );
  }

  Widget _buildPopularCategoriesSection() {
    final popularCategories = CategoryData.getPopularCategories();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SectionHeader(title: 'Popular Categories'),
        const SizedBox(height: AppConstants.paddingMedium),
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingMedium,
          ),
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: AppConstants.paddingMedium,
              mainAxisSpacing: AppConstants.paddingMedium,
              childAspectRatio: 2.2,
            ),
            itemCount: popularCategories.length,
            itemBuilder: (context, index) {
              final category = popularCategories[index];
              return PopularCategoryCard(
                category: category,
                onTap: () => _onCategoryTap(category),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAllCategoriesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SectionHeader(
          title: 'All Categories',
          actionText: _filteredCategories.length > 5 ? 'View All' : null,
          onActionPressed: _filteredCategories.length > 5
              ? () {
                  // Handle view all action
                }
              : null,
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingMedium,
          ),
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _filteredCategories.length,
            separatorBuilder: (context, index) =>
                const SizedBox(height: AppConstants.paddingMedium),
            itemBuilder: (context, index) {
              final category = _filteredCategories[index];
              return ExpandableCategoryCard(
                category: category,
                onCategoryTap: () => _onCategoryTap(category),
                onSubcategoryTap: (subcategory) =>
                    _onSubcategoryTap(category, subcategory),
              );
            },
          ),
        ),
      ],
    );
  }

  void _onCategoryTap(Category category) {
    // Navigate to category products screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Navigate to ${category.name} products'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _onSubcategoryTap(Category category, String subcategory) {
    // Navigate to subcategory products screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Navigate to ${category.name} > $subcategory'),
        duration: const Duration(seconds: 1),
      ),
    );
  }
}
