C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\.dart_tool\\flutter_build\\61a6218e948f07d2c8fae34f29cca279\\app.dill: C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\main.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\.dart_tool\\flutter_build\\dart_plugin_registrant.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\dart_plugin_registrant.dart C:\\src\\flutter\\packages\\flutter\\lib\\material.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\core\\theme\\app_theme.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\presentation\\screens\\main_navigation_screen.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_android-2.2.17\\lib\\path_provider_android.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_android-2.4.1\\lib\\sqflite_android.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\lib\\path_provider_foundation.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_darwin-2.4.2\\lib\\sqflite_darwin.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\path_provider_linux.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\path_provider_windows.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\about.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\action_buttons.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\action_chip.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\action_icons_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\adaptive_text_selection_toolbar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\app.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\app_bar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\app_bar_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\arc.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\autocomplete.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\badge.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\badge_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\banner.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\banner_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_app_bar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_app_bar_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_sheet.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_sheet_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\button.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\button_bar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\button_bar_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\button_style.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\button_style_button.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\button_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\calendar_date_picker.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\card.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\card_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\carousel.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox_list_tile.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\chip.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\chip_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\choice_chip.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\circle_avatar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\color_scheme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\colors.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\constants.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\curves.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\data_table.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\data_table_source.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\data_table_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\date.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\date_picker.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\date_picker_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\debug.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar_button.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\dialog.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\dialog_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\divider.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\divider_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\drawer.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\drawer_header.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\drawer_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown_menu.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown_menu_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\elevated_button.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\elevated_button_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\elevation_overlay.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\expand_icon.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_panel.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_tile.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_tile_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\filled_button.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\filled_button_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\filter_chip.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\flexible_space_bar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button_location.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\grid_tile.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\grid_tile_bar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\icon_button.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\icon_button_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\icons.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\ink_decoration.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\ink_highlight.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\ink_ripple.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\ink_sparkle.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\ink_splash.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\ink_well.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\input_border.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\input_chip.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\input_date_picker_form_field.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\input_decorator.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\list_tile.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\list_tile_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\magnifier.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\material.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\material_button.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\material_localizations.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\material_state.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\material_state_mixin.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\menu_anchor.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\menu_bar_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\menu_button_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\menu_style.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\menu_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\mergeable_material.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\motion.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_bar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_bar_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_drawer.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_drawer_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_rail.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_rail_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\no_splash.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\outlined_button.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\outlined_button_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\page.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\page_transitions_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\paginated_data_table.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\popup_menu.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\popup_menu_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\predictive_back_page_transitions_builder.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\progress_indicator.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\progress_indicator_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\radio.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\radio_list_tile.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\radio_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\range_slider.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\refresh_indicator.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\reorderable_list.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\scaffold.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\scrollbar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\scrollbar_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\search.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\search_anchor.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\search_bar_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\search_view_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\segmented_button.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\segmented_button_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\selectable_text.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\selection_area.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\shadows.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\slider.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\slider_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\slider_value_indicator_shape.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\snack_bar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\snack_bar_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar_layout_delegate.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\stepper.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\switch.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\switch_list_tile.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\switch_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\tab_bar_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\tab_controller.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\tab_indicator.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\tabs.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\text_button.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\text_button_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\text_field.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\text_form_field.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar_text_button.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\text_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\theme_data.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\time.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\time_picker.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\time_picker_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\toggle_buttons.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\toggle_buttons_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip_visibility.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\typography.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\user_accounts_drawer_header.dart C:\\src\\flutter\\packages\\flutter\\lib\\widgets.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\google_fonts.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\core\\constants\\app_colors.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\core\\constants\\app_constants.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\presentation\\screens\\home_screen.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\presentation\\screens\\category_screen.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\presentation\\screens\\wishlist_screen.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\presentation\\screens\\cart_screen.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\presentation\\screens\\profile_screen.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\path_provider_platform_interface.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_android-2.2.17\\lib\\messages.g.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_platform_interface-2.4.0\\lib\\sqflite_platform_interface.dart C:\\src\\flutter\\packages\\flutter\\lib\\foundation.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\lib\\messages.g.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\path_provider_linux.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\folders.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\path_provider_windows_real.dart C:\\src\\flutter\\packages\\flutter\\lib\\cupertino.dart C:\\src\\flutter\\packages\\flutter\\lib\\scheduler.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\back_button.dart C:\\src\\flutter\\packages\\flutter\\lib\\rendering.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons_data.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\add_event.g.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\arrow_menu.g.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\close_menu.g.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\ellipsis_search.g.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\event_add.g.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\home_menu.g.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\list_view.g.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_arrow.g.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_close.g.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_home.g.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\pause_play.g.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\play_pause.g.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\search_ellipsis.g.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\view_list.g.dart C:\\src\\flutter\\packages\\flutter\\lib\\services.dart C:\\src\\flutter\\packages\\flutter\\lib\\animation.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\vector_math_64.dart C:\\src\\flutter\\packages\\flutter\\lib\\gestures.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\collection.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\material_color_utilities.dart C:\\src\\flutter\\packages\\flutter\\lib\\painting.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\characters.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\actions.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\adapter.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_cross_fade.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_scroll_view.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_size.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_switcher.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\annotated_region.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\app.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\app_lifecycle_listener.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\async.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\autocomplete.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\autofill.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\automatic_keep_alive.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\banner.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\basic.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\binding.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\bottom_navigation_bar_item.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\color_filter.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\container.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\context_menu_button_item.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\context_menu_controller.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\debug.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\decorated_sliver.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\default_selection_style.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\default_text_editing_shortcuts.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\desktop_text_selection_toolbar_layout_delegate.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\dismissible.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\display_feature_sub_screen.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\disposable_build_context.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\drag_boundary.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\drag_target.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\draggable_scrollable_sheet.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\dual_transition_builder.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\editable_text.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\expansible.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\fade_in_image.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\feedback.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\flutter_logo.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_manager.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_scope.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_traversal.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\form.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\framework.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\gesture_detector.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\grid_paper.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\heroes.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_data.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_theme_data.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\image.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\image_filter.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\image_icon.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\implicit_animations.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_model.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_notifier.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\interactive_viewer.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\keyboard_listener.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\layout_builder.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\list_wheel_scroll_view.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\localizations.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\lookup_boundary.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\magnifier.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\media_query.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\modal_barrier.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigation_toolbar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigator.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigator_pop_handler.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\nested_scroll_view.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\notification_listener.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\orientation_builder.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\overflow_bar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\overlay.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\overscroll_indicator.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\page_storage.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\page_view.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\pages.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\performance_overlay.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\pinned_header_sliver.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\placeholder.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_menu_bar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_selectable_region_context_menu.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_view.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\pop_scope.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\preferred_size.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\primary_scroll_controller.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\raw_keyboard_listener.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\raw_menu_anchor.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\reorderable_list.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\restoration.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\restoration_properties.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\router.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\routes.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\safe_area.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_activity.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_aware_image_provider.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_configuration.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_context.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_controller.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_delegate.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_metrics.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_notification.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_notification_observer.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_physics.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_position.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_position_with_single_context.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_simulation.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_view.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollable.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollable_helpers.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollbar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\selectable_region.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\selection_container.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\semantics_debugger.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\service_extensions.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\shared_app_data.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\shortcuts.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\single_child_scroll_view.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\size_changed_layout_notifier.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_fill.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_floating_header.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_layout_builder.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_persistent_header.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_prototype_extent_list.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_resizing_header.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_tree.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\slotted_render_object_widget.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\snapshot_widget.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\spacer.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\spell_check.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\standard_component_type.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\status_transitions.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\system_context_menu.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\table.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\tap_region.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\text.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_editing_intents.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_anchors.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_layout_delegate.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\texture.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\ticker_provider.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\title.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\toggleable.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\transitions.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\tween_animation_builder.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_scroll_view.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_viewport.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\undo_history.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\unique_widget.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\value_listenable_builder.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\view.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\viewport.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\visibility.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_inspector.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_preview.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_span.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_state.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\will_pop_scope.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_base.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_a.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_b.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_c.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_d.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_e.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_f.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_g.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_h.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_i.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_j.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_k.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_l.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_m.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_n.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_o.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_p.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_q.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_r.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_s.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_t.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_u.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_v.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_w.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_x.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_y.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_parts\\part_z.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\data\\models\\category.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\data\\repositories\\product_repository.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\presentation\\widgets\\banner_widget.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\presentation\\widgets\\category_item.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\presentation\\widgets\\product_card.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\presentation\\widgets\\section_header.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\presentation\\widgets\\category_search_bar.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\presentation\\widgets\\popular_category_card.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\presentation\\widgets\\expandable_category_card.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\data\\models\\product.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\presentation\\widgets\\wishlist_item_card.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\data\\models\\cart_item.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\presentation\\widgets\\cart_item_card.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cached_network_image-3.4.1\\lib\\cached_network_image.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\data\\models\\user.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\presentation\\widgets\\profile_menu_item.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\lib\\plugin_platform_interface.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\src\\enums.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\src\\method_channel_path_provider.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\sqflite.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\sqlite_api.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_platform_interface-2.4.0\\lib\\src\\factory_platform.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.16.0\\lib\\meta.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\annotations.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\assertions.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\basic_types.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\binding.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\bitfield.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\capabilities.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\change_notifier.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\collections.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\consolidate_response.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\constants.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\debug.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\diagnostics.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\isolates.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\key.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\licenses.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\memory_allocations.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\node.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\object.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\observer_list.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\persistent_hash_map.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\platform.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\print.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\serialization.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\service_extensions.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\stack_frame.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\synchronous_future.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\timeline.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\unicode.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\path.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xdg_directories-1.1.0\\lib\\xdg_directories.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\get_application_id.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.4\\lib\\ffi.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\guid.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\win32_wrappers.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\activity_indicator.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\adaptive_text_selection_toolbar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\app.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\bottom_tab_bar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\button.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\checkbox.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\colors.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\constants.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\context_menu.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\context_menu_action.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\date_picker.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\debug.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar_button.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\dialog.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\form_row.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\form_section.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\icon_theme_data.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\icons.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\interface_level.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\list_section.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\list_tile.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\localizations.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\magnifier.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\nav_bar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\page_scaffold.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\picker.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\radio.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\refresh.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\route.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\scrollbar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\search_field.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\segmented_control.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\sheet.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\slider.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\sliding_segmented_control.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\spell_check_suggestions_toolbar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\switch.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\tab_scaffold.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\tab_view.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_field.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_form_field_row.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar_button.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\theme.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\cupertino\\thumb_painter.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\scheduler\\binding.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\scheduler\\debug.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\scheduler\\priority.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\scheduler\\service_extensions.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\scheduler\\ticker.dart C:\\src\\flutter\\packages\\flutter\\lib\\semantics.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\animated_size.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\binding.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\box.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\custom_layout.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\custom_paint.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\debug.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\debug_overflow_indicator.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\decorated_sliver.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\editable.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\error.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\flex.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\flow.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\image.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\layer.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\layout_helper.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\list_body.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\list_wheel_viewport.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\mouse_tracker.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\object.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\paragraph.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\performance_overlay.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\platform_view.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\proxy_box.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\proxy_sliver.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\rotated_box.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\selection.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\service_extensions.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\shifted_box.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_fill.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_fixed_extent_list.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_grid.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_group.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_list.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_multi_box_adaptor.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_padding.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_persistent_header.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_tree.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\stack.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\table.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\table_border.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\texture.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\tweens.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\view.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\viewport.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\viewport_offset.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\rendering\\wrap.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\asset_bundle.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\asset_manifest.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\autofill.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\binary_messenger.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\binding.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\browser_context_menu.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\clipboard.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\debug.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\deferred_component.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\flavor.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\flutter_version.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\font_loader.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\haptic_feedback.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\hardware_keyboard.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_inserted_content.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_key.g.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_maps.g.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\live_text.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\message_codec.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\message_codecs.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\mouse_cursor.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\mouse_tracking.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\platform_channel.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\platform_views.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\predictive_back_event.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\process_text.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_android.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_fuchsia.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_ios.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_linux.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_macos.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_web.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_windows.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\restoration.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\scribe.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\service_extensions.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\spell_check.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\system_channels.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\system_chrome.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\system_navigator.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\system_sound.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\text_boundary.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\text_editing.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\text_editing_delta.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\text_formatter.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\text_input.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\text_layout_metrics.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\undo_manager.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\animation\\animation.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\animation\\animation_controller.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\animation\\animation_style.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\animation\\animations.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\animation\\curves.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\animation\\listener_helpers.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\animation\\tween.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\animation\\tween_sequence.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb2.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb3.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\colors.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\constants.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\error_helpers.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\frustum.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\intersection_result.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix2.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix3.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix4.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\noise.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\obb3.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\opengl.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\plane.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quad.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quaternion.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\ray.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\sphere.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\triangle.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\utilities.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector2.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector3.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector4.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\arena.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\binding.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\constants.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\converter.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\debug.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\drag.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\drag_details.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\eager.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\events.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\force_press.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\gesture_settings.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\hit_test.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\long_press.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\lsq_solver.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\monodrag.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\multidrag.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\multitap.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\pointer_router.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\pointer_signal_resolver.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\recognizer.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\resampler.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\scale.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\tap.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\tap_and_drag.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\team.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\gestures\\velocity_tracker.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\algorithms.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\boollist.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\canonicalized_map.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_iterable.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_list.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_map.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\comparators.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\equality.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\equality_map.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\equality_set.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\functions.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\iterable_extensions.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\iterable_zip.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\list_extensions.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\priority_queue.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\queue_list.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\union_set.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\union_set_controller.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\unmodifiable_wrappers.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\wrappers.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\blend\\blend.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\contrast\\contrast.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dislike\\dislike_analyzer.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\dynamic_color.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\dynamic_scheme.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\material_dynamic_colors.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\variant.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\cam16.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\hct.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\viewing_conditions.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\palettes\\core_palette.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\palettes\\tonal_palette.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_celebi.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_map.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_wsmeans.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_wu.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_content.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_expressive.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_fidelity.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_fruit_salad.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_monochrome.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_neutral.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_rainbow.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_tonal_spot.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_vibrant.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\score\\score.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\temperature\\temperature_cache.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\color_utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\math_utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\string_utils.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\alignment.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\basic_types.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\beveled_rectangle_border.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\binding.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\border_radius.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\borders.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\box_border.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\box_decoration.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\box_fit.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\box_shadow.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\circle_border.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\clip.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\colors.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\continuous_rectangle_border.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\debug.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\decoration.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\decoration_image.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\edge_insets.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\flutter_logo.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\fractional_offset.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\geometry.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\gradient.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\image_cache.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\image_decoder.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\image_provider.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\image_resolution.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\image_stream.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\inline_span.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\linear_border.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\matrix_utils.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\notched_shapes.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\oval_border.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\paint_utilities.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\placeholder_span.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\rounded_rectangle_border.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\shader_warm_up.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\shape_decoration.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\stadium_border.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\star_border.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\strut_style.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\text_painter.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\text_scaler.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\text_span.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\text_style.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\characters.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\extensions.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\constants.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\_web_image_info_io.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\_web_image_io.dart C:\\src\\flutter\\packages\\flutter\\lib\\physics.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\_platform_selectable_region_context_menu_io.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\widgets\\_html_element_view_io.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.16.0\\lib\\meta_meta.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\crypto.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\http.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\asset_manifest.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\file_io_desktop_and_mobile.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_descriptor.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_family_with_variant.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\lib\\src\\google_fonts_variant.dart C:\\Users\\<USER>\ Damra\\Documents\\augment-projects\\JerichoSales\\shop_app\\lib\\presentation\\widgets\\subcategory_item.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\flutter_cache_manager.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cached_network_image-3.4.1\\lib\\src\\cached_image_widget.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cached_network_image-3.4.1\\lib\\src\\image_provider\\cached_network_image_provider.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cached_network_image-3.4.1\\lib\\src\\image_provider\\multi_image_stream_completer.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\lib\\platform.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\sqflite_database_factory.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\sql.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\database.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\database_mixin.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\open_options.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\transaction.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\constant.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\exception.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\sqflite_debug.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\mixin\\platform.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_platform_interface-2.4.0\\lib\\src\\platform_exception.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_platform_interface-2.4.0\\lib\\src\\sqflite_import.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_platform_interface-2.4.0\\lib\\src\\sqflite_method_channel.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\_bitfield_io.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\_capabilities_io.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\_isolates_io.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\_platform_io.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\foundation\\_timeline_io.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\context.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\path_exception.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\path_map.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\path_set.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\get_application_id_real.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.4\\lib\\src\\allocation.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.4\\lib\\src\\arena.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.4\\lib\\src\\utf16.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.4\\lib\\src\\utf8.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\semantics\\binding.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\semantics\\debug.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics_event.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics_service.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\services\\_background_isolate_binary_messenger_io.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_iterator.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\empty_unmodifiable_set.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\src\\contrast_curve.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\src\\tone_delta_pair.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\src\\hct_solver.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\src\\point_provider_lab.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\src\\point_provider.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\painting\\_network_image_io.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\characters_impl.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\physics\\clamped_simulation.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\physics\\friction_simulation.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\physics\\gravity_simulation.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\physics\\simulation.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\physics\\spring_simulation.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\physics\\tolerance.dart C:\\src\\flutter\\packages\\flutter\\lib\\src\\physics\\utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\digest.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\hash.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\hmac.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\md5.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\sha1.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\sha256.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\sha512.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\client.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\exception.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\request.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\response.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\streamed_request.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\base_client.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\base_request.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\base_response.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\byte_stream.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\multipart_file.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\multipart_request.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\streamed_response.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider-2.1.5\\lib\\path_provider.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\cache_manager.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\cache_managers\\cache_managers.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\compat\\file_fetcher.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\config\\config.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\logger.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\result\\result.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\storage\\cache_info_repositories\\cache_info_repositories.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\storage\\cache_object.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\storage\\file_system\\file_system.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\web\\file_service.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\web\\web_helper.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cached_network_image_platform_interface-4.1.1\\lib\\cached_network_image_platform_interface.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\octo_image-2.1.0\\lib\\octo_image.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cached_network_image-3.4.1\\lib\\src\\image_provider\\_image_loader.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\interface\\local_platform.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\interface\\platform.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\testing\\fake_platform.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\factory.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\sql_builder.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\batch.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\cursor.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\collection_utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\path_utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\value_utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\utils\\utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\synchronized-3.3.1\\lib\\synchronized.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\arg_utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\mixin\\import_mixin.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\logger\\sqflite_logger.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\database_file_system.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\platform\\platform.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\compat.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\factory_mixin.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\mixin\\constant.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\mixin\\factory.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\characters.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\internal_style.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\parsed_path.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style\\posix.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style\\url.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style\\windows.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\grapheme_clusters\\breaks.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\grapheme_clusters\\constants.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\grapheme_clusters\\table.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\digest_sink.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\hash_sink.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\sha512_fastsinks.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\io_client.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\http_parser.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\multipart_file_io.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\boundary_characters.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\file.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\cache_store.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\uuid.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\cache_managers\\base_cache_manager.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\cache_managers\\default_cache_manager.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\cache_managers\\image_cache_manager.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\config\\_config_io.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\result\\download_progress.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\result\\file_info.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\result\\file_response.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\storage\\cache_info_repositories\\cache_info_repository.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\storage\\cache_info_repositories\\cache_object_provider.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\storage\\cache_info_repositories\\json_cache_info_repository.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\storage\\cache_info_repositories\\non_storing_object_provider.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\lib\\clock.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\storage\\file_system\\file_system_io.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\storage\\file_system\\file_system_web.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\web\\mime_converter.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\web\\queue_item.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\rxdart.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\octo_image-2.1.0\\lib\\src\\errors.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\octo_image-2.1.0\\lib\\src\\image\\image.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\octo_image-2.1.0\\lib\\src\\image_transformers.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\octo_image-2.1.0\\lib\\src\\octo_set.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\octo_image-2.1.0\\lib\\src\\placeholders.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\octo_image-2.1.0\\lib\\src\\progress_indicators.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\sql_command.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\env_utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\synchronized-3.3.1\\lib\\src\\basic_lock.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\synchronized-3.3.1\\lib\\src\\reentrant_lock.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\synchronized-3.3.1\\lib\\src\\multi_lock.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\sqflite_logger.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\platform\\platform_io.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\lib\\typed_data.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\io_streamed_response.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\authentication_challenge.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\case_insensitive_map.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\chunked_coding.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\http_date.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\media_type.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\data.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\rng.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\validation.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\enums.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\parsing.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\uuid_value.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\v1.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\v4.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\v5.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\v6.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\v7.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\v8.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\v8generic.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\lib\\src\\storage\\cache_info_repositories\\helper_methods.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.4.2\\lib\\sqflite.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\lib\\src\\default.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\lib\\src\\clock.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\local.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\memory.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\rx.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\streams.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\subjects.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\transformers.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\octo_image-2.1.0\\lib\\src\\image\\image_handler.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\synchronized-3.3.1\\lib\\src\\utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\database_file_system_io.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\lib\\src\\typed_queue.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\lib\\typed_buffers.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\string_scanner.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\scan.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\chunked_coding\\decoder.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\chunked_coding\\encoder.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_directory.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_file.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_file_system.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_file_system_entity.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_link.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_random_access_file.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\directory.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\error_codes.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\file.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\file_system.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\file_system_entity.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\link.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\io.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\constants.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fixnum-1.1.1\\lib\\fixnum.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sprintf-7.0.0\\lib\\sprintf.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.4.2\\lib\\src\\compat.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.4.2\\lib\\src\\constant.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.4.2\\lib\\src\\sqflite_android.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.4.2\\lib\\src\\sqflite_impl.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.4.2\\lib\\src\\utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.4.2\\lib\\utils\\utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.4.2\\lib\\sqlite_api.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.4.2\\lib\\sql.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.4.2\\lib\\src\\factory_impl.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.4.2\\lib\\src\\sqflite_darwin.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.4.2\\lib\\src\\sqflite_plugin.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\lib\\src\\stopwatch.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\lib\\src\\utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\memory.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\memory\\operations.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\error_and_stacktrace.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\combine_latest.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\concat.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\concat_eager.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\connectable_stream.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\defer.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\fork_join.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\from_callable.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\merge.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\never.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\race.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\range.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\repeat.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\replay_stream.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\retry.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\retry_when.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\sequence_equal.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\switch_latest.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\timer.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\using.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\value_stream.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\zip.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\subjects\\behavior_subject.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\subjects\\publish_subject.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\subjects\\replay_subject.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\subjects\\subject.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\buffer.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\debounce.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\pairwise.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\sample.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\throttle.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\window.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\default_if_empty.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\delay.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\delay_when.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\dematerialize.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\distinct_unique.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\do.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\end_with.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\end_with_many.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\exhaust_map.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\flat_map.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\group_by.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\ignore_elements.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\interval.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\map_not_null.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\map_to.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\materialize.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\max.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\min.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\on_error_resume.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\scan.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\skip_last.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\skip_until.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\start_with.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\start_with_many.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\switch_if_empty.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\switch_map.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\take_last.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\take_until.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\take_while_inclusive.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\time_interval.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\timestamp.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\where_not_null.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\where_type.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\with_latest_from.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\composite_subscription.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\notification.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\octo_image-2.1.0\\lib\\src\\image\\fade_widget.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\lib\\src\\typed_buffer.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\exception.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\line_scanner.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\span_scanner.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\string_scanner.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\source_span.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\chunked_coding\\charcodes.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\error_codes_dart_io.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fixnum-1.1.1\\lib\\src\\int32.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fixnum-1.1.1\\lib\\src\\int64.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fixnum-1.1.1\\lib\\src\\intx.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sprintf-7.0.0\\lib\\src\\formatters\\Formatter.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sprintf-7.0.0\\lib\\src\\formatters\\int_formatter.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sprintf-7.0.0\\lib\\src\\formatters\\float_formatter.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sprintf-7.0.0\\lib\\src\\formatters\\string_formatter.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sprintf-7.0.0\\lib\\src\\sprintf_impl.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.4.2\\lib\\src\\sqflite_import.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.4.2\\lib\\src\\services_impl.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.4.2\\lib\\src\\sql_builder.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.4.2\\lib\\src\\exception_impl.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite-2.4.2\\lib\\src\\dev_utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_file_system.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\memory\\memory_file_system.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\memory\\style.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\collection_extensions.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\subscription.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\start_with_error.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\empty.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\backpressure.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\forwarding_sink.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\forwarding_stream.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\future.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\min_max.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\charcode.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\eager_span_scanner.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\relative_span_scanner.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\file.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\location.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\location_mixin.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\span.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\span_exception.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\span_mixin.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\span_with_context.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fixnum-1.1.1\\lib\\src\\utilities.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\lib\\src\\dev_utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_directory.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_file.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_link.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\memory\\clock.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\memory\\common.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\memory\\memory_directory.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\memory\\memory_file.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\memory\\memory_file_stat.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\memory\\memory_link.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\memory\\node.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\memory\\utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\lib\\term_glyph.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\highlighter.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\utils.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\common.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_file_system_entity.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\memory\\memory_file_system_entity.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\memory\\memory_random_access_file.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\lib\\src\\generated\\ascii_glyph_set.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\lib\\src\\generated\\glyph_set.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\lib\\src\\generated\\unicode_glyph_set.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\lib\\src\\generated\\top_level.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\charcode.dart C:\\Users\\<USER>\ Damra\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\colors.dart
