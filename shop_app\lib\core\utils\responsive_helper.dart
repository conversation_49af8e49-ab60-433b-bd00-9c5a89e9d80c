import 'package:flutter/material.dart';
import '../constants/app_constants.dart';

class ResponsiveHelper {
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < AppConstants.mobileBreakpoint;
  }

  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= AppConstants.mobileBreakpoint && 
           width < AppConstants.tabletBreakpoint;
  }

  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= AppConstants.tabletBreakpoint;
  }

  static double getResponsivePadding(BuildContext context) {
    if (isMobile(context)) {
      return AppConstants.paddingMedium;
    } else if (isTablet(context)) {
      return AppConstants.paddingLarge;
    } else {
      return AppConstants.paddingXLarge;
    }
  }

  static double getResponsiveIconSize(BuildContext context, {
    double mobileSize = AppConstants.iconMedium,
    double tabletSize = AppConstants.iconLarge,
    double desktopSize = AppConstants.iconXLarge,
  }) {
    if (isMobile(context)) {
      return mobileSize;
    } else if (isTablet(context)) {
      return tabletSize;
    } else {
      return desktopSize;
    }
  }

  static int getGridCrossAxisCount(BuildContext context) {
    if (isMobile(context)) {
      return AppConstants.gridCrossAxisCount;
    } else if (isTablet(context)) {
      return AppConstants.gridCrossAxisCountTablet;
    } else {
      return AppConstants.gridCrossAxisCountDesktop;
    }
  }

  static double getGridChildAspectRatio(BuildContext context) {
    if (isMobile(context)) {
      return AppConstants.gridChildAspectRatio;
    } else {
      return AppConstants.gridChildAspectRatioTablet;
    }
  }

  static TextStyle? getResponsiveTextStyle(
    BuildContext context, {
    required TextStyle? mobileStyle,
    TextStyle? tabletStyle,
    TextStyle? desktopStyle,
  }) {
    if (isMobile(context)) {
      return mobileStyle;
    } else if (isTablet(context)) {
      return tabletStyle ?? mobileStyle;
    } else {
      return desktopStyle ?? tabletStyle ?? mobileStyle;
    }
  }

  static double getResponsiveValue(
    BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
  }) {
    if (isMobile(context)) {
      return mobile;
    } else if (isTablet(context)) {
      return tablet ?? mobile;
    } else {
      return desktop ?? tablet ?? mobile;
    }
  }

  static EdgeInsets getResponsiveMargin(BuildContext context) {
    return EdgeInsets.symmetric(
      horizontal: getResponsivePadding(context),
      vertical: AppConstants.paddingSmall,
    );
  }

  static double getResponsiveCardHeight(BuildContext context) {
    if (isMobile(context)) {
      return 260;
    } else if (isTablet(context)) {
      return 300;
    } else {
      return 320;
    }
  }

  static double getResponsiveImageHeight(BuildContext context) {
    if (isMobile(context)) {
      return AppConstants.productImageHeight;
    } else {
      return AppConstants.productImageHeightTablet;
    }
  }

  static double getResponsiveBorderRadius(BuildContext context) {
    if (isMobile(context)) {
      return AppConstants.radiusCard;
    } else {
      return AppConstants.radiusXLarge;
    }
  }

  static double getResponsiveButtonHeight(BuildContext context) {
    if (isMobile(context)) {
      return AppConstants.buttonHeightMedium;
    } else {
      return AppConstants.buttonHeightLarge;
    }
  }

  static double getResponsiveAppBarHeight(BuildContext context) {
    if (isMobile(context)) {
      return AppConstants.appBarHeight;
    } else {
      return AppConstants.appBarHeight + 8;
    }
  }

  static double getResponsiveBottomNavHeight(BuildContext context) {
    if (isMobile(context)) {
      return AppConstants.bottomNavHeight;
    } else {
      return AppConstants.bottomNavHeight + 8;
    }
  }
}
