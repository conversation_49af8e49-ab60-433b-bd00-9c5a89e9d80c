{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\augment-projects\\JerichoSales\\shop_app\\build\\.cxx\\Debug\\1ju1x1j2\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\augment-projects\\JerichoSales\\shop_app\\build\\.cxx\\Debug\\1ju1x1j2\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}